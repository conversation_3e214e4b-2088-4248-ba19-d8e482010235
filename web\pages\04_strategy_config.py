#!/usr/bin/env python3
"""
策略参数配置页面
Strategy Parameter Configuration Page

提供EMA策略参数的动态配置功能，支持预设模板和自定义参数
"""

import streamlit as st
import pandas as pd
import json
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from src.strategies.ema_breakout_strategy import EMABreakoutStrategy
    from web.utils.tbtrade_integration import tbtrade_integration
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="Strategy Configuration - TBTrade",
    page_icon="⚙️",
    layout="wide"
)

# 预设参数模板
PRESET_TEMPLATES = {
    "默认配置": {
        "ema_short": 21,
        "ema_medium": 55,
        "ema_long": 200,
        "stop_loss_pct": 0.15,
        "partial_profit_pct": 0.50,
        "signal_cooldown_days": 30,
        "slope_periods": 3,
        "slope_confirm_periods": 3,
        "min_ema200_periods": 200,
        "description": "标准EMA突破策略配置，适合大多数市场环境"
    },
    "保守型": {
        "ema_short": 21,
        "ema_medium": 55,
        "ema_long": 200,
        "stop_loss_pct": 0.10,
        "partial_profit_pct": 0.30,
        "signal_cooldown_days": 45,
        "slope_periods": 5,
        "slope_confirm_periods": 5,
        "min_ema200_periods": 200,
        "description": "保守策略，较小止损，较长冷却期，适合稳健投资者"
    },
    "平衡型": {
        "ema_short": 21,
        "ema_medium": 55,
        "ema_long": 200,
        "stop_loss_pct": 0.15,
        "partial_profit_pct": 0.50,
        "signal_cooldown_days": 30,
        "slope_periods": 3,
        "slope_confirm_periods": 3,
        "min_ema200_periods": 200,
        "description": "平衡策略，标准参数配置，风险收益平衡"
    },
    "激进型": {
        "ema_short": 12,
        "ema_medium": 26,
        "ema_long": 100,
        "stop_loss_pct": 0.20,
        "partial_profit_pct": 0.80,
        "signal_cooldown_days": 15,
        "slope_periods": 2,
        "slope_confirm_periods": 2,
        "min_ema200_periods": 100,
        "description": "激进策略，更短周期，更高风险收益比"
    },
    "短线型": {
        "ema_short": 9,
        "ema_medium": 21,
        "ema_long": 55,
        "stop_loss_pct": 0.08,
        "partial_profit_pct": 0.25,
        "signal_cooldown_days": 7,
        "slope_periods": 2,
        "slope_confirm_periods": 2,
        "min_ema200_periods": 55,
        "description": "短线策略，快速响应，适合短期交易"
    },
    "长线型": {
        "ema_short": 50,
        "ema_medium": 100,
        "ema_long": 200,
        "stop_loss_pct": 0.25,
        "partial_profit_pct": 1.00,
        "signal_cooldown_days": 60,
        "slope_periods": 5,
        "slope_confirm_periods": 5,
        "min_ema200_periods": 200,
        "description": "长线策略，较大容忍度，适合长期持有"
    }
}

def load_current_config() -> Dict[str, Any]:
    """加载当前策略配置"""
    try:
        # 尝试从配置文件加载
        config_path = project_root / "config" / "strategy.json"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('custom_params', {}).get('EMABreakout', {})
    except Exception as e:
        st.warning(f"加载配置文件失败: {e}")
    
    # 返回默认配置
    return EMABreakoutStrategy.get_default_parameters()

def save_config(params: Dict[str, Any]) -> bool:
    """保存策略配置"""
    try:
        config_path = project_root / "config" / "strategy.json"
        
        # 读取现有配置
        config = {}
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        
        # 更新EMA策略参数
        if 'custom_params' not in config:
            config['custom_params'] = {}
        config['custom_params']['EMABreakout'] = params
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        st.error(f"保存配置失败: {e}")
        return False

def validate_parameters(params: Dict[str, Any]) -> tuple[bool, str]:
    """验证参数合理性"""
    try:
        # 基本范围检查
        if not (5 <= params['ema_short'] <= 50):
            return False, "短期EMA周期应在5-50之间"
        
        if not (20 <= params['ema_medium'] <= 100):
            return False, "中期EMA周期应在20-100之间"
        
        if not (50 <= params['ema_long'] <= 300):
            return False, "长期EMA周期应在50-300之间"
        
        # 逻辑关系检查
        if params['ema_short'] >= params['ema_medium']:
            return False, "短期EMA周期必须小于中期EMA周期"
        
        if params['ema_medium'] >= params['ema_long']:
            return False, "中期EMA周期必须小于长期EMA周期"
        
        # 风险参数检查
        if not (0.01 <= params['stop_loss_pct'] <= 0.50):
            return False, "止损比例应在1%-50%之间"
        
        if not (0.10 <= params['partial_profit_pct'] <= 2.00):
            return False, "分批止盈比例应在10%-200%之间"
        
        if not (1 <= params['signal_cooldown_days'] <= 365):
            return False, "信号冷却期应在1-365天之间"
        
        return True, "参数验证通过"
        
    except Exception as e:
        return False, f"参数验证错误: {e}"

# 初始化session state
if 'current_params' not in st.session_state:
    st.session_state.current_params = load_current_config()

if 'selected_template' not in st.session_state:
    st.session_state.selected_template = "默认配置"

# 页面标题
st.title("⚙️ 策略参数配置")
st.markdown("---")

# 创建标签页
tab1, tab2, tab3 = st.tabs(["📋 参数配置", "📊 参数预览", "💾 配置管理"])

with tab1:
    st.header("EMA策略参数配置")
    
    # 预设模板选择
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_template = st.selectbox(
            "选择预设模板",
            list(PRESET_TEMPLATES.keys()),
            index=list(PRESET_TEMPLATES.keys()).index(st.session_state.selected_template),
            help="选择预设的策略参数模板，可以基于模板进行自定义修改"
        )
    
    with col2:
        if st.button("应用模板", type="primary"):
            st.session_state.current_params = PRESET_TEMPLATES[selected_template].copy()
            st.session_state.selected_template = selected_template
            # 移除description字段
            if 'description' in st.session_state.current_params:
                del st.session_state.current_params['description']
            st.success(f"已应用 {selected_template} 模板")
            st.rerun()
    
    # 显示模板描述
    if selected_template in PRESET_TEMPLATES:
        st.info(f"**{selected_template}**: {PRESET_TEMPLATES[selected_template]['description']}")
    
    st.divider()
    
    # 参数配置区域
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 EMA指标参数")
        
        ema_short = st.slider(
            "短期EMA周期",
            min_value=5, max_value=50,
            value=st.session_state.current_params.get('ema_short', 21),
            help="短期EMA的计算周期，用于捕捉短期趋势"
        )
        
        ema_medium = st.slider(
            "中期EMA周期",
            min_value=20, max_value=100,
            value=st.session_state.current_params.get('ema_medium', 55),
            help="中期EMA的计算周期，用于确认趋势方向"
        )
        
        ema_long = st.slider(
            "长期EMA周期",
            min_value=50, max_value=300,
            value=st.session_state.current_params.get('ema_long', 200),
            help="长期EMA的计算周期，用于判断主要趋势"
        )
        
        st.subheader("📊 技术分析参数")
        
        slope_periods = st.slider(
            "斜率计算周期",
            min_value=1, max_value=10,
            value=st.session_state.current_params.get('slope_periods', 3),
            help="计算EMA斜率的周期数"
        )
        
        slope_confirm_periods = st.slider(
            "斜率确认周期",
            min_value=1, max_value=10,
            value=st.session_state.current_params.get('slope_confirm_periods', 3),
            help="斜率确认需要的连续周期数"
        )
        
        min_ema200_periods = st.slider(
            "EMA200最少数据点",
            min_value=50, max_value=300,
            value=st.session_state.current_params.get('min_ema200_periods', 200),
            help="计算EMA200所需的最少历史数据点"
        )
    
    with col2:
        st.subheader("🛡️ 风险管理参数")
        
        stop_loss_pct = st.slider(
            "止损比例 (%)",
            min_value=1.0, max_value=50.0,
            value=st.session_state.current_params.get('stop_loss_pct', 0.15) * 100,
            step=0.5,
            help="固定止损的百分比"
        ) / 100
        
        partial_profit_pct = st.slider(
            "分批止盈比例 (%)",
            min_value=10.0, max_value=200.0,
            value=st.session_state.current_params.get('partial_profit_pct', 0.50) * 100,
            step=5.0,
            help="触发分批止盈的盈利百分比"
        ) / 100
        
        signal_cooldown_days = st.slider(
            "信号冷却期 (天)",
            min_value=1, max_value=365,
            value=st.session_state.current_params.get('signal_cooldown_days', 30),
            help="两次信号之间的最小间隔天数"
        )
        
        st.subheader("⚡ 其他参数")
        
        daily_close_hour = st.slider(
            "日线收盘时间 (小时)",
            min_value=0, max_value=23,
            value=st.session_state.current_params.get('daily_close_hour', 8),
            help="日线数据的收盘时间（24小时制）"
        )
    
    # 更新参数
    new_params = {
        'ema_short': ema_short,
        'ema_medium': ema_medium,
        'ema_long': ema_long,
        'stop_loss_pct': stop_loss_pct,
        'partial_profit_pct': partial_profit_pct,
        'signal_cooldown_days': signal_cooldown_days,
        'slope_periods': slope_periods,
        'slope_confirm_periods': slope_confirm_periods,
        'min_ema200_periods': min_ema200_periods,
        'daily_close_hour': daily_close_hour
    }
    
    # 参数验证
    is_valid, validation_msg = validate_parameters(new_params)
    
    if is_valid:
        st.success(f"✅ {validation_msg}")
        st.session_state.current_params = new_params
    else:
        st.error(f"❌ {validation_msg}")

with tab2:
    st.header("📊 参数预览和影响分析")
    
    # 参数对比表
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("当前参数配置")
        params_df = pd.DataFrame([
            {"参数名称": "短期EMA周期", "当前值": st.session_state.current_params.get('ema_short', 21), "单位": "周期"},
            {"参数名称": "中期EMA周期", "当前值": st.session_state.current_params.get('ema_medium', 55), "单位": "周期"},
            {"参数名称": "长期EMA周期", "当前值": st.session_state.current_params.get('ema_long', 200), "单位": "周期"},
            {"参数名称": "止损比例", "当前值": f"{st.session_state.current_params.get('stop_loss_pct', 0.15)*100:.1f}", "单位": "%"},
            {"参数名称": "分批止盈比例", "当前值": f"{st.session_state.current_params.get('partial_profit_pct', 0.50)*100:.1f}", "单位": "%"},
            {"参数名称": "信号冷却期", "当前值": st.session_state.current_params.get('signal_cooldown_days', 30), "单位": "天"},
        ])
        st.dataframe(params_df, use_container_width=True, hide_index=True)
    
    with col2:
        st.subheader("参数影响分析")
        
        # 风险等级评估
        risk_score = 0
        if st.session_state.current_params.get('stop_loss_pct', 0.15) > 0.20:
            risk_score += 2
        elif st.session_state.current_params.get('stop_loss_pct', 0.15) > 0.15:
            risk_score += 1
        
        if st.session_state.current_params.get('signal_cooldown_days', 30) < 15:
            risk_score += 2
        elif st.session_state.current_params.get('signal_cooldown_days', 30) < 30:
            risk_score += 1
        
        if st.session_state.current_params.get('ema_short', 21) < 15:
            risk_score += 1
        
        if risk_score <= 1:
            risk_level = "🟢 低风险"
            risk_color = "green"
        elif risk_score <= 3:
            risk_level = "🟡 中等风险"
            risk_color = "orange"
        else:
            risk_level = "🔴 高风险"
            risk_color = "red"
        
        st.markdown(f"**风险等级**: :{risk_color}[{risk_level}]")
        
        # 策略特征分析
        if st.session_state.current_params.get('ema_short', 21) < 15:
            st.info("📈 短期响应敏感，适合捕捉快速趋势")
        
        if st.session_state.current_params.get('signal_cooldown_days', 30) > 45:
            st.info("⏰ 交易频率较低，适合长期投资")
        
        if st.session_state.current_params.get('stop_loss_pct', 0.15) < 0.10:
            st.warning("⚠️ 止损较紧，可能增加止损频率")

with tab3:
    st.header("💾 配置管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("保存配置")
        
        if st.button("💾 保存当前配置", type="primary", use_container_width=True):
            if save_config(st.session_state.current_params):
                st.success("✅ 配置保存成功！")
                st.balloons()
            else:
                st.error("❌ 配置保存失败")
        
        if st.button("🔄 重置为默认配置", use_container_width=True):
            st.session_state.current_params = EMABreakoutStrategy.get_default_parameters()
            st.session_state.selected_template = "默认配置"
            st.success("✅ 已重置为默认配置")
            st.rerun()
    
    with col2:
        st.subheader("配置状态")
        
        current_config = load_current_config()
        if current_config == st.session_state.current_params:
            st.success("✅ 当前配置已保存")
        else:
            st.warning("⚠️ 配置已修改，尚未保存")
        
        st.info(f"📅 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 配置导出/导入
    st.divider()
    st.subheader("配置导出/导入")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**导出配置**")
        config_json = json.dumps(st.session_state.current_params, indent=2, ensure_ascii=False)
        st.download_button(
            label="📥 下载配置文件",
            data=config_json,
            file_name=f"ema_strategy_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
    
    with col2:
        st.markdown("**导入配置**")
        uploaded_file = st.file_uploader("选择配置文件", type=['json'])
        if uploaded_file is not None:
            try:
                imported_config = json.load(uploaded_file)
                is_valid, msg = validate_parameters(imported_config)
                if is_valid:
                    st.session_state.current_params = imported_config
                    st.success("✅ 配置导入成功！")
                    st.rerun()
                else:
                    st.error(f"❌ 配置文件无效: {msg}")
            except Exception as e:
                st.error(f"❌ 配置文件格式错误: {e}")
