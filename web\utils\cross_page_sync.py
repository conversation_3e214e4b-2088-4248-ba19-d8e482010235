#!/usr/bin/env python3
"""
跨页面数据同步工具
Cross-Page Data Synchronization Utility

确保异步回测数据在所有Streamlit页面间正确同步
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import datetime
import threading
import time

class CrossPageSync:
    """跨页面数据同步管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式确保全局唯一实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(CrossPageSync, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        
        self.last_sync_time = None
        self.sync_interval = 5  # 5秒同步间隔
        self._initialized = True
    
    def ensure_data_sync(self):
        """确保数据同步 - 在每个页面开始时调用"""
        try:
            current_time = datetime.now()
            
            # 检查是否需要同步
            if (self.last_sync_time is None or 
                (current_time - self.last_sync_time).seconds >= self.sync_interval):
                
                self._perform_sync()
                self.last_sync_time = current_time
                
        except Exception as e:
            print(f"❌ 数据同步失败: {e}")
    
    def _perform_sync(self):
        """执行数据同步"""
        try:
            # 导入必要的模块
            from .data_store import data_store
            from .async_backtest_manager import async_backtest_manager
            
            # 强制刷新数据存储
            data_store.force_sync_all_sources()
            
            # 刷新异步回测管理器
            async_backtest_manager._refresh_all_data_sources()
            
            print(f"✅ 跨页面数据同步完成: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ 执行数据同步失败: {e}")
    
    def force_sync(self):
        """强制立即同步"""
        self._perform_sync()
        self.last_sync_time = datetime.now()
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态信息"""
        return {
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_interval': self.sync_interval,
            'next_sync_in': self.sync_interval - (datetime.now() - self.last_sync_time).seconds if self.last_sync_time else 0
        }

# 全局单例实例
cross_page_sync = CrossPageSync()

def init_page_sync():
    """页面初始化时调用的同步函数"""
    try:
        cross_page_sync.ensure_data_sync()
        
        # 在侧边栏显示同步状态（可选）
        if st.sidebar:
            with st.sidebar:
                sync_status = cross_page_sync.get_sync_status()
                if sync_status['last_sync_time']:
                    last_sync = datetime.fromisoformat(sync_status['last_sync_time'])
                    st.caption(f"🔄 数据同步: {last_sync.strftime('%H:%M:%S')}")
                
                # 手动同步按钮
                if st.button("🔄 手动同步", help="立即同步所有页面数据"):
                    cross_page_sync.force_sync()
                    st.success("数据同步完成！")
                    st.rerun()
                    
    except Exception as e:
        print(f"❌ 页面同步初始化失败: {e}")

def sync_task_data():
    """专门用于同步任务数据的函数"""
    try:
        from .data_store import data_store
        from .async_backtest_manager import async_backtest_manager
        
        # 获取最新的任务数据
        tasks_from_store = data_store.load_tasks()
        completed_tasks = async_backtest_manager.get_completed_tasks()
        
        # 确保session_state中有最新数据
        if 'backtest_tasks' not in st.session_state:
            st.session_state.backtest_tasks = {}
        
        # 更新session_state
        for task in completed_tasks:
            st.session_state.backtest_tasks[task.task_id] = task.to_dict()
        
        print(f"✅ 任务数据同步完成: {len(completed_tasks)}个任务")
        return True
        
    except Exception as e:
        print(f"❌ 任务数据同步失败: {e}")
        return False

def get_synced_completed_tasks():
    """获取同步后的已完成任务列表"""
    try:
        # 确保数据同步
        cross_page_sync.ensure_data_sync()
        sync_task_data()
        
        # 从多个数据源获取任务
        from .async_backtest_manager import async_backtest_manager
        from .data_store import data_store
        
        completed_tasks = []
        
        # 方法1: 从异步回测管理器获取
        try:
            tasks_from_manager = async_backtest_manager.get_completed_tasks()
            completed_tasks.extend(tasks_from_manager)
        except Exception as e:
            print(f"从回测管理器获取数据失败: {e}")
        
        # 方法2: 从数据存储获取
        try:
            tasks_from_store = data_store.get_completed_tasks()
            # 转换为BacktestTask对象格式
            for task_data in tasks_from_store:
                if not any(t.task_id == task_data['task_id'] for t in completed_tasks):
                    # 创建一个简单的任务对象
                    class SimpleTask:
                        def __init__(self, task_id, config, result):
                            self.task_id = task_id
                            self.config = config
                            self.result = result
                    
                    task = SimpleTask(
                        task_data['task_id'],
                        task_data['config'],
                        task_data['result']
                    )
                    completed_tasks.append(task)
        except Exception as e:
            print(f"从数据存储获取数据失败: {e}")
        
        print(f"✅ 获取到 {len(completed_tasks)} 个已完成任务")
        return completed_tasks
        
    except Exception as e:
        print(f"❌ 获取同步任务失败: {e}")
        return []

def display_sync_info():
    """显示同步信息（用于调试）"""
    try:
        sync_status = cross_page_sync.get_sync_status()
        
        st.sidebar.markdown("---")
        st.sidebar.markdown("### 🔄 数据同步状态")
        
        if sync_status['last_sync_time']:
            last_sync = datetime.fromisoformat(sync_status['last_sync_time'])
            st.sidebar.info(f"上次同步: {last_sync.strftime('%H:%M:%S')}")
        else:
            st.sidebar.warning("尚未同步")
        
        # 显示任务统计
        try:
            completed_tasks = get_synced_completed_tasks()
            st.sidebar.metric("已完成任务", len(completed_tasks))
        except Exception as e:
            st.sidebar.error(f"获取任务统计失败: {e}")
            
    except Exception as e:
        print(f"❌ 显示同步信息失败: {e}")
