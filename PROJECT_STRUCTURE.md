# TBTrade 项目结构

**生成时间**: 2025-07-12 13:53:30

## 📁 核心目录结构

```
TBTrade/
├── src/                    # 核心业务逻辑
│   ├── core/              # 核心模块
│   ├── strategies/        # 交易策略
│   ├── services/          # 服务层
│   └── utils/             # 工具函数
├── web/                   # Web界面系统
│   ├── pages/             # Streamlit页面
│   ├── components/        # UI组件
│   ├── utils/             # Web工具
│   └── data/              # Web数据
├── tools/                 # 监控工具
├── config/                # 配置文件
├── data/                  # 数据库文件
├── logs/                  # 日志文件
├── tests/                 # 测试文件
├── docs/                  # 项目文档
├── mydoc/                 # 开发日志
└── backtest_results/      # 回测结果
```

## 📊 文件统计

- **src/**: 35 个文件 (35 Python, 0 JSON)
- **web/**: 42 个文件 (24 Python, 1 JSON)
- **tools/**: 5 个文件 (5 Python, 0 JSON)
- **config/**: 14 个文件 (0 Python, 9 JSON)
- **tests/**: 14 个文件 (14 Python, 0 JSON)
- **docs/**: 5 个文件 (0 Python, 0 JSON)

## 🎯 主要功能模块

1. **监控系统** (`tools/auto_4h_monitor.py`)
2. **Web界面** (`web/streamlit_app.py`)
3. **策略引擎** (`src/strategies/`)
4. **数据管理** (`src/data_layer/`)
5. **配置管理** (`src/core/config_manager.py`)
6. **通知服务** (`src/services/`)

## 📝 最近更新

- 第一阶段：数据层集成完成
- 第二阶段：监控系统实际控制完成
- 项目清理：优化文件结构，删除冗余文件
