#!/usr/bin/env python3
"""
Web集成全面测试
Comprehensive Web Integration Test
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def test_web_integration():
    """全面测试Web集成功能"""
    print("🔍 Web集成全面测试")
    print("=" * 60)
    
    # 创建集成实例
    integration = TBTradeIntegration()
    
    # 测试计数器
    tests_passed = 0
    tests_failed = 0
    
    def test_result(test_name, success, details=""):
        nonlocal tests_passed, tests_failed
        if success:
            tests_passed += 1
            print(f"✅ {test_name}")
            if details:
                print(f"   {details}")
        else:
            tests_failed += 1
            print(f"❌ {test_name}")
            if details:
                print(f"   {details}")
    
    # 1. 数据库连接测试
    print("\n📊 数据库连接测试")
    print("-" * 30)
    
    try:
        db_status = integration.check_database_connection()
        test_result(
            "数据库连接检查", 
            db_status['connected'],
            f"发现{db_status['databases_found']}个数据库，{db_status['total_symbols']}个币种"
        )
    except Exception as e:
        test_result("数据库连接检查", False, f"异常: {e}")
    
    # 2. 币种数据测试
    print("\n💰 币种数据测试")
    print("-" * 30)
    
    try:
        symbols = integration.get_available_symbols()
        test_result(
            "获取可用币种", 
            len(symbols) > 0,
            f"获取到{len(symbols)}个币种"
        )
        
        # 测试缓存
        start_time = time.time()
        symbols2 = integration.get_available_symbols()
        cache_time = time.time() - start_time
        test_result(
            "币种数据缓存", 
            cache_time < 0.01 and symbols == symbols2,
            f"缓存命中，耗时{cache_time:.4f}秒"
        )
    except Exception as e:
        test_result("币种数据获取", False, f"异常: {e}")
    
    # 3. 数据信息测试
    print("\n📈 数据信息测试")
    print("-" * 30)
    
    try:
        data_info = integration.get_data_info()
        test_result(
            "获取数据概览", 
            'error' not in data_info,
            f"总数据库: {data_info.get('total_databases', 0)}, 总K线: {data_info.get('total_klines', 0):,}"
        )
        
        # 测试特定币种数据
        if symbols:
            symbol_info = integration.get_data_info(symbols[0])
            test_result(
                f"获取{symbols[0]}数据信息", 
                'error' not in symbol_info,
                f"K线数: {symbol_info.get('total_klines', 0):,}"
            )
    except Exception as e:
        test_result("数据信息获取", False, f"异常: {e}")
    
    # 4. 系统状态测试
    print("\n🖥️ 系统状态测试")
    print("-" * 30)
    
    try:
        system_status = integration.get_system_status()
        test_result(
            "获取系统状态", 
            'error' not in system_status,
            f"数据库: {'连接' if system_status.get('database', {}).get('connected') else '断开'}"
        )
        
        # 测试监控状态
        monitor_info = system_status.get('monitor', {})
        test_result(
            "监控状态集成", 
            'status_message' in monitor_info,
            f"监控状态: {monitor_info.get('status_message', 'unknown')}"
        )
    except Exception as e:
        test_result("系统状态获取", False, f"异常: {e}")
    
    # 5. 监控状态测试
    print("\n📡 监控状态测试")
    print("-" * 30)
    
    try:
        monitor_status = integration.get_monitor_status()
        test_result(
            "获取监控状态", 
            'status_message' in monitor_status,
            f"状态: {monitor_status['status_message']}, 币种数: {monitor_status['monitored_symbols']}"
        )
        
        # 测试K线时间计算
        test_result(
            "K线时间计算", 
            monitor_status['next_kline_time'] is not None,
            f"下个K线: {monitor_status['next_kline_time']}"
        )
    except Exception as e:
        test_result("监控状态获取", False, f"异常: {e}")
    
    # 6. 数据更新测试
    print("\n🔄 数据更新测试")
    print("-" * 30)
    
    try:
        update_status = integration.get_data_update_status()
        test_result(
            "数据更新状态检查", 
            'status' in update_status,
            f"数据状态: {update_status.get('status', 'unknown')}"
        )
        
        # 测试触发更新（仅逻辑测试）
        update_trigger = integration.trigger_data_update(['BTCUSDT'])
        test_result(
            "数据更新触发", 
            'status' in update_trigger,
            f"触发结果: {update_trigger.get('status', 'unknown')}"
        )
    except Exception as e:
        test_result("数据更新功能", False, f"异常: {e}")
    
    # 7. 策略信号测试
    print("\n📊 策略信号测试")
    print("-" * 30)
    
    try:
        # 初始化告警数据库
        init_result = integration.init_alerts_database()
        test_result("告警数据库初始化", init_result)
        
        # 获取信号统计
        signal_stats = integration.get_signal_statistics()
        test_result(
            "信号统计获取", 
            'error' not in signal_stats,
            f"总信号: {signal_stats.get('total_signals', 0)}"
        )
        
        # 获取信号数据
        signals = integration.get_strategy_signals(limit=10)
        test_result(
            "信号数据查询", 
            'error' not in signals,
            f"返回信号: {signals.get('statistics', {}).get('returned_signals', 0)}"
        )
    except Exception as e:
        test_result("策略信号功能", False, f"异常: {e}")
    
    # 8. 缓存系统测试
    print("\n💾 缓存系统测试")
    print("-" * 30)
    
    try:
        cache_status = integration.get_cache_status()
        test_result(
            "缓存状态获取", 
            'total_cached_items' in cache_status,
            f"缓存项: {cache_status.get('total_cached_items', 0)}"
        )
        
        # 测试缓存清除
        integration._clear_cache('symbols')
        cache_status_after = integration.get_cache_status()
        test_result(
            "缓存清除功能", 
            cache_status_after['total_cached_items'] < cache_status['total_cached_items'],
            "缓存项数量减少"
        )
    except Exception as e:
        test_result("缓存系统功能", False, f"异常: {e}")
    
    # 9. 数据加载测试
    print("\n📋 数据加载测试")
    print("-" * 30)
    
    try:
        if symbols:
            # 测试加载币种数据
            symbol_data = integration.load_symbol_data(symbols[0])
            test_result(
                f"加载{symbols[0]}数据", 
                len(symbol_data) > 0,
                f"数据行数: {len(symbol_data)}"
            )
    except Exception as e:
        test_result("数据加载功能", False, f"异常: {e}")
    
    # 10. 性能测试
    print("\n⚡ 性能测试")
    print("-" * 30)
    
    try:
        # 测试多次调用的性能
        start_time = time.time()
        for _ in range(5):
            integration.get_available_symbols()
        total_time = time.time() - start_time
        
        test_result(
            "缓存性能测试", 
            total_time < 0.1,
            f"5次调用总耗时: {total_time:.4f}秒"
        )
    except Exception as e:
        test_result("性能测试", False, f"异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    total_tests = tests_passed + tests_failed
    success_rate = (tests_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {tests_passed} ✅")
    print(f"失败测试: {tests_failed} ❌")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 测试结果: 优秀！Web集成功能完全正常")
    elif success_rate >= 80:
        print("\n👍 测试结果: 良好！大部分功能正常工作")
    elif success_rate >= 70:
        print("\n⚠️ 测试结果: 一般，需要修复部分问题")
    else:
        print("\n❌ 测试结果: 需要重大修复")
    
    return success_rate >= 80

if __name__ == "__main__":
    test_web_integration()
