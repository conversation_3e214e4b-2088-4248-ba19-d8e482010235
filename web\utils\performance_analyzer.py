#!/usr/bin/env python3
"""
策略性能分析器
Strategy Performance Analyzer

提供高级性能指标计算、策略对比分析和策略评分推荐功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import math

class PerformanceAnalyzer:
    """策略性能分析器"""
    
    def __init__(self):
        self.risk_free_rate = 0.03  # 无风险利率 3%
        
    def calculate_advanced_metrics(self, equity_curve: pd.DataFrame, 
                                 trades: pd.DataFrame, 
                                 initial_capital: float = 10000) -> Dict[str, Any]:
        """
        计算高级性能指标
        
        Args:
            equity_curve: 资产曲线数据
            trades: 交易记录
            initial_capital: 初始资金
            
        Returns:
            Dict: 高级性能指标
        """
        try:
            metrics = {}
            
            # 基础指标
            if not equity_curve.empty and 'equity' in equity_curve.columns:
                returns = equity_curve['equity'].pct_change().dropna()
                final_value = equity_curve['equity'].iloc[-1]
                
                # 总收益率
                total_return = (final_value - initial_capital) / initial_capital
                metrics['total_return'] = total_return
                
                # 年化收益率
                days = len(equity_curve)
                if days > 0:
                    annual_return = (1 + total_return) ** (365 / days) - 1
                    metrics['annual_return'] = annual_return
                else:
                    metrics['annual_return'] = 0
                
                # 波动率
                if len(returns) > 1:
                    volatility = returns.std() * np.sqrt(252)  # 年化波动率
                    metrics['volatility'] = volatility
                    
                    # 夏普比率
                    if volatility > 0:
                        sharpe_ratio = (annual_return - self.risk_free_rate) / volatility
                        metrics['sharpe_ratio'] = sharpe_ratio
                    else:
                        metrics['sharpe_ratio'] = 0
                else:
                    metrics['volatility'] = 0
                    metrics['sharpe_ratio'] = 0
                
                # 最大回撤
                peak = equity_curve['equity'].expanding().max()
                drawdown = (equity_curve['equity'] - peak) / peak
                max_drawdown = drawdown.min()
                metrics['max_drawdown'] = abs(max_drawdown)
                
                # 卡尔玛比率 (Calmar Ratio)
                if max_drawdown < 0:
                    calmar_ratio = annual_return / abs(max_drawdown)
                    metrics['calmar_ratio'] = calmar_ratio
                else:
                    metrics['calmar_ratio'] = 0
                
                # 索提诺比率 (Sortino Ratio)
                negative_returns = returns[returns < 0]
                if len(negative_returns) > 0:
                    downside_deviation = negative_returns.std() * np.sqrt(252)
                    if downside_deviation > 0:
                        sortino_ratio = (annual_return - self.risk_free_rate) / downside_deviation
                        metrics['sortino_ratio'] = sortino_ratio
                    else:
                        metrics['sortino_ratio'] = 0
                else:
                    metrics['sortino_ratio'] = float('inf')
                
            else:
                # 如果没有资产曲线数据，设置默认值
                metrics.update({
                    'total_return': 0,
                    'annual_return': 0,
                    'volatility': 0,
                    'sharpe_ratio': 0,
                    'max_drawdown': 0,
                    'calmar_ratio': 0,
                    'sortino_ratio': 0
                })
            
            # 交易相关指标
            if not trades.empty:
                # 确保有必要的列
                if 'pnl' in trades.columns:
                    pnl_series = trades['pnl']
                    
                    # 胜率
                    winning_trades = len(pnl_series[pnl_series > 0])
                    total_trades = len(pnl_series)
                    win_rate = winning_trades / total_trades if total_trades > 0 else 0
                    metrics['win_rate'] = win_rate
                    
                    # 盈亏比
                    avg_win = pnl_series[pnl_series > 0].mean() if winning_trades > 0 else 0
                    avg_loss = abs(pnl_series[pnl_series < 0].mean()) if len(pnl_series[pnl_series < 0]) > 0 else 0
                    profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
                    metrics['profit_loss_ratio'] = profit_loss_ratio
                    
                    # 最大连续盈利/亏损
                    consecutive_wins = self._calculate_consecutive_wins_losses(pnl_series)
                    metrics.update(consecutive_wins)
                    
                    # 期望收益
                    expected_return = pnl_series.mean()
                    metrics['expected_return'] = expected_return
                    
                    # 盈利因子
                    total_profit = pnl_series[pnl_series > 0].sum()
                    total_loss = abs(pnl_series[pnl_series < 0].sum())
                    profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
                    metrics['profit_factor'] = profit_factor
                    
                else:
                    # 如果没有pnl列，设置默认值
                    metrics.update({
                        'win_rate': 0,
                        'profit_loss_ratio': 0,
                        'max_consecutive_wins': 0,
                        'max_consecutive_losses': 0,
                        'expected_return': 0,
                        'profit_factor': 0
                    })
                
                # 交易频率
                metrics['total_trades'] = len(trades)
                if not equity_curve.empty:
                    days = len(equity_curve)
                    trades_per_month = len(trades) / (days / 30) if days > 0 else 0
                    metrics['trades_per_month'] = trades_per_month
                else:
                    metrics['trades_per_month'] = 0
                    
            else:
                # 如果没有交易数据，设置默认值
                metrics.update({
                    'win_rate': 0,
                    'profit_loss_ratio': 0,
                    'max_consecutive_wins': 0,
                    'max_consecutive_losses': 0,
                    'expected_return': 0,
                    'profit_factor': 0,
                    'total_trades': 0,
                    'trades_per_month': 0
                })
            
            return metrics
            
        except Exception as e:
            print(f"计算高级指标失败: {e}")
            return self._get_default_metrics()
    
    def _calculate_consecutive_wins_losses(self, pnl_series: pd.Series) -> Dict[str, int]:
        """计算最大连续盈利和亏损次数"""
        try:
            if len(pnl_series) == 0:
                return {'max_consecutive_wins': 0, 'max_consecutive_losses': 0}
            
            max_wins = 0
            max_losses = 0
            current_wins = 0
            current_losses = 0
            
            for pnl in pnl_series:
                if pnl > 0:
                    current_wins += 1
                    current_losses = 0
                    max_wins = max(max_wins, current_wins)
                elif pnl < 0:
                    current_losses += 1
                    current_wins = 0
                    max_losses = max(max_losses, current_losses)
                else:
                    current_wins = 0
                    current_losses = 0
            
            return {
                'max_consecutive_wins': max_wins,
                'max_consecutive_losses': max_losses
            }
            
        except Exception:
            return {'max_consecutive_wins': 0, 'max_consecutive_losses': 0}
    
    def _get_default_metrics(self) -> Dict[str, Any]:
        """获取默认指标值"""
        return {
            'total_return': 0,
            'annual_return': 0,
            'volatility': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'calmar_ratio': 0,
            'sortino_ratio': 0,
            'win_rate': 0,
            'profit_loss_ratio': 0,
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0,
            'expected_return': 0,
            'profit_factor': 0,
            'total_trades': 0,
            'trades_per_month': 0
        }
    
    def compare_strategies(self, strategy_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        策略对比分析
        
        Args:
            strategy_results: 多个策略的结果列表
            
        Returns:
            Dict: 对比分析结果
        """
        try:
            if not strategy_results:
                return {'error': '没有策略结果可供对比'}
            
            comparison = {
                'strategies': [],
                'rankings': {},
                'best_strategy': {},
                'summary': {}
            }
            
            # 计算每个策略的高级指标
            for i, result in enumerate(strategy_results):
                strategy_name = result.get('name', f'策略{i+1}')
                equity_curve = result.get('equity_curve', pd.DataFrame())
                trades = result.get('trades', pd.DataFrame())
                initial_capital = result.get('initial_capital', 10000)
                
                metrics = self.calculate_advanced_metrics(equity_curve, trades, initial_capital)
                metrics['name'] = strategy_name
                metrics['config'] = result.get('config', {})
                
                comparison['strategies'].append(metrics)
            
            # 计算排名
            ranking_metrics = ['total_return', 'sharpe_ratio', 'calmar_ratio', 'win_rate', 'profit_factor']
            
            for metric in ranking_metrics:
                sorted_strategies = sorted(
                    comparison['strategies'], 
                    key=lambda x: x.get(metric, 0), 
                    reverse=True
                )
                comparison['rankings'][metric] = [s['name'] for s in sorted_strategies]
            
            # 找出最佳策略（综合评分）
            best_strategy = self._calculate_strategy_scores(comparison['strategies'])
            comparison['best_strategy'] = best_strategy
            
            # 生成对比摘要
            comparison['summary'] = self._generate_comparison_summary(comparison['strategies'])
            
            return comparison
            
        except Exception as e:
            return {'error': f'策略对比分析失败: {e}'}
    
    def _calculate_strategy_scores(self, strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算策略综合评分"""
        try:
            if not strategies:
                return {}
            
            # 评分权重
            weights = {
                'total_return': 0.25,
                'sharpe_ratio': 0.20,
                'max_drawdown': 0.15,  # 负向指标，越小越好
                'win_rate': 0.15,
                'profit_factor': 0.15,
                'calmar_ratio': 0.10
            }
            
            # 标准化指标值
            normalized_strategies = []
            
            for strategy in strategies:
                normalized = strategy.copy()
                score = 0
                
                for metric, weight in weights.items():
                    value = strategy.get(metric, 0)
                    
                    if metric == 'max_drawdown':
                        # 回撤越小越好，转换为正向指标
                        normalized_value = max(0, 1 - value)
                    else:
                        # 其他指标越大越好
                        normalized_value = max(0, value)
                    
                    score += normalized_value * weight
                
                normalized['composite_score'] = score
                normalized_strategies.append(normalized)
            
            # 找出最高评分的策略
            best_strategy = max(normalized_strategies, key=lambda x: x['composite_score'])
            
            return best_strategy
            
        except Exception as e:
            print(f"计算策略评分失败: {e}")
            return {}
    
    def _generate_comparison_summary(self, strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成对比摘要"""
        try:
            if not strategies:
                return {}
            
            summary = {
                'total_strategies': len(strategies),
                'best_return': max(s.get('total_return', 0) for s in strategies),
                'best_sharpe': max(s.get('sharpe_ratio', 0) for s in strategies),
                'lowest_drawdown': min(s.get('max_drawdown', 1) for s in strategies),
                'highest_win_rate': max(s.get('win_rate', 0) for s in strategies),
                'avg_return': np.mean([s.get('total_return', 0) for s in strategies]),
                'avg_sharpe': np.mean([s.get('sharpe_ratio', 0) for s in strategies])
            }
            
            return summary
            
        except Exception as e:
            print(f"生成对比摘要失败: {e}")
            return {}
    
    def generate_strategy_recommendation(self, user_profile: Dict[str, Any], 
                                       available_strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成策略推荐
        
        Args:
            user_profile: 用户风险偏好配置
            available_strategies: 可用策略列表
            
        Returns:
            Dict: 策略推荐结果
        """
        try:
            if not available_strategies:
                return {'error': '没有可用的策略'}
            
            # 用户风险偏好
            risk_tolerance = user_profile.get('risk_tolerance', 'medium')  # low, medium, high
            return_target = user_profile.get('return_target', 0.15)  # 目标年化收益率
            max_drawdown_tolerance = user_profile.get('max_drawdown_tolerance', 0.20)  # 最大可接受回撤
            
            # 筛选符合条件的策略
            suitable_strategies = []
            
            for strategy in available_strategies:
                # 检查回撤是否在可接受范围内
                if strategy.get('max_drawdown', 1) <= max_drawdown_tolerance:
                    # 根据风险偏好评分
                    score = self._calculate_recommendation_score(
                        strategy, risk_tolerance, return_target
                    )
                    strategy['recommendation_score'] = score
                    suitable_strategies.append(strategy)
            
            # 按推荐分数排序
            suitable_strategies.sort(key=lambda x: x['recommendation_score'], reverse=True)
            
            recommendation = {
                'user_profile': user_profile,
                'total_available': len(available_strategies),
                'suitable_count': len(suitable_strategies),
                'recommendations': suitable_strategies[:5],  # 推荐前5个
                'explanation': self._generate_recommendation_explanation(
                    user_profile, suitable_strategies[:3] if suitable_strategies else []
                )
            }
            
            return recommendation
            
        except Exception as e:
            return {'error': f'生成策略推荐失败: {e}'}
    
    def _calculate_recommendation_score(self, strategy: Dict[str, Any], 
                                      risk_tolerance: str, return_target: float) -> float:
        """计算推荐评分"""
        try:
            score = 0
            
            # 基础评分
            total_return = strategy.get('total_return', 0)
            sharpe_ratio = strategy.get('sharpe_ratio', 0)
            max_drawdown = strategy.get('max_drawdown', 1)
            win_rate = strategy.get('win_rate', 0)
            
            # 收益评分
            if total_return >= return_target:
                score += 30
            else:
                score += (total_return / return_target) * 30
            
            # 风险调整评分
            if risk_tolerance == 'low':
                # 保守型：重视稳定性
                score += sharpe_ratio * 20
                score += (1 - max_drawdown) * 25
                score += win_rate * 25
            elif risk_tolerance == 'medium':
                # 平衡型：平衡收益和风险
                score += sharpe_ratio * 25
                score += (1 - max_drawdown) * 20
                score += win_rate * 15
                score += min(total_return * 10, 10)
            else:  # high
                # 激进型：追求高收益
                score += total_return * 20
                score += sharpe_ratio * 15
                score += (1 - max_drawdown) * 10
                score += min(strategy.get('profit_factor', 0) * 5, 15)
            
            return max(0, score)
            
        except Exception:
            return 0
    
    def _generate_recommendation_explanation(self, user_profile: Dict[str, Any], 
                                           top_strategies: List[Dict[str, Any]]) -> str:
        """生成推荐说明"""
        try:
            risk_tolerance = user_profile.get('risk_tolerance', 'medium')
            
            if not top_strategies:
                return "根据您的风险偏好，暂时没有找到合适的策略推荐。"
            
            explanations = []
            
            risk_desc = {
                'low': '保守型',
                'medium': '平衡型', 
                'high': '激进型'
            }
            
            explanations.append(f"根据您的{risk_desc.get(risk_tolerance, '平衡型')}风险偏好，")
            
            if len(top_strategies) >= 1:
                best = top_strategies[0]
                explanations.append(f"推荐策略 '{best.get('name', '策略1')}'，")
                explanations.append(f"该策略年化收益率为 {best.get('annual_return', 0):.1%}，")
                explanations.append(f"最大回撤 {best.get('max_drawdown', 0):.1%}，")
                explanations.append(f"夏普比率 {best.get('sharpe_ratio', 0):.2f}。")
            
            return ''.join(explanations)
            
        except Exception:
            return "推荐说明生成失败。"

# 全局实例
performance_analyzer = PerformanceAnalyzer()
