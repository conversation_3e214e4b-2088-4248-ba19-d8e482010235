# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log

# 数据库文件 (保留结构，忽略大文件)
*.db-journal
*.db-wal
*.db-shm

# 回测结果 (保留最新的几个)
backtest_results/dynamic_backtest_*
backtest_results/ema_optimized_backtest_*

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 测试覆盖率
.coverage
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# 配置文件中的敏感信息
config/*_secure.json
config/*.key