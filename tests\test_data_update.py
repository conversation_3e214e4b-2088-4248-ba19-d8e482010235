#!/usr/bin/env python3
"""
测试数据更新功能
Test Data Update Functionality
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def test_data_update():
    """测试数据更新功能"""
    print("🔍 测试数据更新功能...")
    
    # 创建集成实例
    integration = TBTradeIntegration()
    
    # 1. 测试获取数据更新状态
    print("\n1. 获取数据更新状态:")
    update_status = integration.get_data_update_status()
    print(f"   状态: {update_status.get('status', 'unknown')}")
    print(f"   消息: {update_status.get('message', 'N/A')}")
    print(f"   最后更新: {update_status.get('latest_update', 'N/A')}")
    print(f"   距离更新: {update_status.get('hours_since_update', 0):.1f} 小时")
    print(f"   建议更新: {'是' if update_status.get('update_recommended', False) else '否'}")
    
    if 'error' in update_status:
        print(f"   错误: {update_status['error']}")
        return
    
    # 2. 测试触发数据更新（仅检查逻辑，不实际执行）
    print("\n2. 测试触发数据更新逻辑:")
    try:
        # 使用少量币种进行测试
        test_symbols = ['BTCUSDT', 'ETHUSDT']
        update_result = integration.trigger_data_update(symbols=test_symbols)
        
        print(f"   更新状态: {update_result.get('status', 'unknown')}")
        print(f"   消息: {update_result.get('message', 'N/A')}")
        
        if update_result.get('status') == 'updating':
            print(f"   币种: {update_result.get('symbols', [])}")
            print(f"   时间范围: {update_result.get('start_time', 'N/A')} ~ {update_result.get('end_time', 'N/A')}")
            print(f"   间隔: {update_result.get('interval', 'N/A')}")
        elif update_result.get('status') == 'no_update_needed':
            print(f"   时间差: {update_result.get('time_diff_hours', 0):.1f} 小时")
        elif update_result.get('status') == 'error':
            print(f"   错误: {update_result.get('error', 'N/A')}")
            
    except Exception as e:
        print(f"   异常: {e}")
    
    # 3. 测试系统状态（包含数据更新信息）
    print("\n3. 系统状态概览:")
    system_status = integration.get_system_status()
    
    if 'error' not in system_status:
        db_info = system_status.get('database', {})
        data_info = system_status.get('data', {})
        
        print(f"   数据库连接: {'✅' if db_info.get('connected', False) else '❌'}")
        print(f"   总币种数: {db_info.get('total_symbols', 0)}")
        print(f"   总K线数: {db_info.get('total_klines', 0):,}")
        print(f"   数据质量: {data_info.get('data_quality', 'unknown')}")
        print(f"   最新更新: {db_info.get('latest_update', 'N/A')}")
    else:
        print(f"   错误: {system_status['error']}")

if __name__ == "__main__":
    test_data_update()
