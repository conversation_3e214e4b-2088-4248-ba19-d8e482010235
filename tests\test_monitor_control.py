#!/usr/bin/env python3
"""
测试监控系统启停控制功能
Test Monitor Control Functionality
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.monitor_integration import MonitorIntegration

def test_monitor_control():
    """测试监控系统启停控制"""
    print("🔍 测试监控系统启停控制功能...")
    
    # 创建监控集成实例
    monitor_integration = MonitorIntegration()
    
    # 1. 测试获取初始状态
    print("\n1. 获取初始监控状态:")
    initial_status = monitor_integration.get_monitor_status()
    print(f"   运行状态: {'🟢 运行中' if initial_status.get('is_running', False) else '🔴 未运行'}")
    print(f"   监控实例: {'存在' if initial_status.get('monitor_instance') else '不存在'}")
    
    # 2. 测试获取监控配置
    print("\n2. 获取监控配置:")
    config = monitor_integration.get_monitor_config()
    if 'error' not in config:
        print(f"   监控间隔: {config.get('monitor_interval', 'N/A')}")
        print(f"   最大币种: {config.get('max_symbols', 'N/A')}")
        print(f"   策略列表: {config.get('strategies', [])}")
        print(f"   数据源: {config.get('data_source', 'N/A')}")
        print(f"   通知启用: {config.get('notification_enabled', False)}")
    else:
        print(f"   错误: {config['error']}")
    
    # 3. 测试启动监控系统
    print("\n3. 测试启动监控系统:")
    print("   正在启动监控系统...")
    
    start_result = monitor_integration.start_monitor()
    print(f"   启动结果: {'✅ 成功' if start_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {start_result.get('message', 'N/A')}")
    print(f"   时间戳: {start_result.get('timestamp', 'N/A')}")
    
    if start_result.get('success', False):
        # 等待一段时间让监控系统启动
        print("   等待监控系统启动...")
        time.sleep(3)
        
        # 4. 检查启动后的状态
        print("\n4. 检查启动后状态:")
        running_status = monitor_integration.get_monitor_status()
        print(f"   运行状态: {'🟢 运行中' if running_status.get('is_running', False) else '🔴 未运行'}")
        print(f"   监控实例: {'存在' if running_status.get('monitor_instance') else '不存在'}")
        print(f"   线程状态: {'活跃' if running_status.get('thread_alive', False) else '非活跃'}")
        
        # 5. 测试重复启动
        print("\n5. 测试重复启动:")
        duplicate_start = monitor_integration.start_monitor()
        print(f"   重复启动结果: {'✅ 正确拒绝' if not duplicate_start.get('success', True) else '❌ 错误允许'}")
        print(f"   消息: {duplicate_start.get('message', 'N/A')}")
        
        # 6. 测试停止监控系统
        print("\n6. 测试停止监控系统:")
        print("   正在停止监控系统...")
        
        stop_result = monitor_integration.stop_monitor()
        print(f"   停止结果: {'✅ 成功' if stop_result.get('success', False) else '❌ 失败'}")
        print(f"   消息: {stop_result.get('message', 'N/A')}")
        print(f"   时间戳: {stop_result.get('timestamp', 'N/A')}")
        
        # 等待停止完成
        time.sleep(2)
        
        # 7. 检查停止后的状态
        print("\n7. 检查停止后状态:")
        stopped_status = monitor_integration.get_monitor_status()
        print(f"   运行状态: {'🟢 运行中' if stopped_status.get('is_running', False) else '🔴 未运行'}")
        print(f"   监控实例: {'存在' if stopped_status.get('monitor_instance') else '不存在'}")
        
        # 8. 测试重复停止
        print("\n8. 测试重复停止:")
        duplicate_stop = monitor_integration.stop_monitor()
        print(f"   重复停止结果: {'✅ 正确拒绝' if not duplicate_stop.get('success', True) else '❌ 错误允许'}")
        print(f"   消息: {duplicate_stop.get('message', 'N/A')}")
    
    # 9. 测试配置更新
    print("\n9. 测试配置更新:")
    new_config = {
        'monitor_interval': '2h',
        'max_symbols': 100,
        'strategies': ['EMABreakout', 'RSIStrategy'],
        'notification_enabled': False
    }
    
    update_result = monitor_integration.update_monitor_config(new_config)
    print(f"   更新结果: {'✅ 成功' if update_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {update_result.get('message', 'N/A')}")
    
    # 10. 测试告警数据获取
    print("\n10. 测试告警数据获取:")
    try:
        alerts_data = monitor_integration.get_alerts_data()
        print(f"   告警数据行数: {len(alerts_data)}")
        if len(alerts_data) > 0:
            print(f"   最新告警: {alerts_data.iloc[0].to_dict() if not alerts_data.empty else 'N/A'}")
    except Exception as e:
        print(f"   获取告警数据失败: {e}")
    
    print("\n" + "="*60)
    print("🎯 监控控制测试完成")
    print("="*60)

if __name__ == "__main__":
    test_monitor_control()
