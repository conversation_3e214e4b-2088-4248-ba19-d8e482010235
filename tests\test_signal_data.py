#!/usr/bin/env python3
"""
测试策略信号数据功能
Test Strategy Signal Data Functionality
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
import random

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def create_test_signals(integration):
    """创建测试信号数据"""
    print("📊 创建测试信号数据...")
    
    # 测试信号数据
    test_signals = [
        {
            'symbol': 'BTCUSDT',
            'strategy': 'EMABreakout',
            'signal_type': 'BUY',
            'confidence': 0.85,
            'price': 45000.0,
            'reason': 'EMA21 crossed above EMA55',
            'indicators': {
                'ema21': 44800,
                'ema55': 44500,
                'ema200': 43000,
                'volume': 1500000
            },
            'timestamp': (datetime.now() - timedelta(hours=2)).isoformat()
        },
        {
            'symbol': 'ETHUSDT',
            'strategy': 'EMABreakout',
            'signal_type': 'SELL',
            'confidence': 0.75,
            'price': 3200.0,
            'reason': 'EMA21 slope turned negative',
            'indicators': {
                'ema21': 3180,
                'ema55': 3150,
                'ema200': 3100,
                'volume': 800000
            },
            'timestamp': (datetime.now() - timedelta(hours=1)).isoformat()
        },
        {
            'symbol': 'ADAUSDT',
            'strategy': 'EMABreakout',
            'signal_type': 'BUY',
            'confidence': 0.90,
            'price': 0.45,
            'reason': 'Strong EMA alignment and volume surge',
            'indicators': {
                'ema21': 0.44,
                'ema55': 0.43,
                'ema200': 0.41,
                'volume': 2000000
            },
            'timestamp': (datetime.now() - timedelta(minutes=30)).isoformat()
        }
    ]
    
    # 添加测试信号
    for signal in test_signals:
        result = integration.add_strategy_signal(signal)
        if result.get('success'):
            print(f"   ✅ 添加信号: {signal['symbol']} {signal['signal_type']} (ID: {result['signal_id']})")
        else:
            print(f"   ❌ 添加失败: {result.get('error', 'unknown error')}")

def test_signal_data():
    """测试策略信号数据功能"""
    print("🔍 测试策略信号数据功能...")
    
    # 创建集成实例
    integration = TBTradeIntegration()
    
    # 1. 初始化数据库
    print("\n1. 初始化告警数据库:")
    init_result = integration.init_alerts_database()
    print(f"   初始化结果: {'✅ 成功' if init_result else '❌ 失败'}")
    
    # 2. 创建测试数据
    create_test_signals(integration)
    
    # 3. 获取信号统计
    print("\n3. 获取信号统计:")
    stats = integration.get_signal_statistics()
    
    if 'error' not in stats:
        print(f"   总信号数: {stats['total_signals']}")
        print(f"   唯一币种: {stats['unique_symbols']}")
        print(f"   唯一策略: {stats['unique_strategies']}")
        print(f"   信号类型分布: {stats['signal_types']}")
        
        print("   最近活动:")
        for activity in stats['recent_activity'][:5]:
            print(f"     - {activity['timestamp']}: {activity['symbol']} {activity['signal_type']} ({activity['confidence']:.2f})")
    else:
        print(f"   错误: {stats['error']}")
    
    # 4. 查询所有信号
    print("\n4. 查询所有信号:")
    all_signals = integration.get_strategy_signals()
    
    if 'error' not in all_signals:
        print(f"   返回信号数: {all_signals['statistics']['returned_signals']}")
        print(f"   总信号数: {all_signals['statistics']['total_signals']}")
        
        print("   信号详情:")
        for signal in all_signals['signals'][:3]:
            print(f"     - {signal['timestamp']}: {signal['symbol']} {signal['strategy']} {signal['signal_type']}")
            print(f"       价格: {signal['price']}, 置信度: {signal['confidence']:.2f}")
            print(f"       原因: {signal['reason']}")
    else:
        print(f"   错误: {all_signals['error']}")
    
    # 5. 按币种筛选
    print("\n5. 按币种筛选 (BTCUSDT):")
    btc_signals = integration.get_strategy_signals(symbol='BTCUSDT')
    
    if 'error' not in btc_signals:
        print(f"   BTCUSDT信号数: {btc_signals['statistics']['returned_signals']}")
        for signal in btc_signals['signals']:
            print(f"     - {signal['timestamp']}: {signal['signal_type']} @ {signal['price']}")
    else:
        print(f"   错误: {btc_signals['error']}")
    
    # 6. 按策略筛选
    print("\n6. 按策略筛选 (EMABreakout):")
    ema_signals = integration.get_strategy_signals(strategy='EMABreakout')
    
    if 'error' not in ema_signals:
        print(f"   EMABreakout信号数: {ema_signals['statistics']['returned_signals']}")
        print(f"   涉及币种: {ema_signals['statistics']['unique_symbols']}")
    else:
        print(f"   错误: {ema_signals['error']}")
    
    # 7. 按信号类型筛选
    print("\n7. 按信号类型筛选 (BUY):")
    buy_signals = integration.get_strategy_signals(signal_type='BUY')
    
    if 'error' not in buy_signals:
        print(f"   BUY信号数: {buy_signals['statistics']['returned_signals']}")
        for signal in buy_signals['signals']:
            print(f"     - {signal['symbol']}: {signal['confidence']:.2f} @ {signal['price']}")
    else:
        print(f"   错误: {buy_signals['error']}")
    
    # 8. 时间范围筛选
    print("\n8. 时间范围筛选 (最近1小时):")
    recent_time = (datetime.now() - timedelta(hours=1)).isoformat()
    recent_signals = integration.get_strategy_signals(start_date=recent_time)
    
    if 'error' not in recent_signals:
        print(f"   最近1小时信号数: {recent_signals['statistics']['returned_signals']}")
        for signal in recent_signals['signals']:
            print(f"     - {signal['timestamp']}: {signal['symbol']} {signal['signal_type']}")
    else:
        print(f"   错误: {recent_signals['error']}")
    
    # 9. 测试数据库路径
    print("\n9. 数据库信息:")
    alerts_db_path = integration.get_alerts_db_path()
    print(f"   数据库路径: {alerts_db_path}")
    print(f"   数据库存在: {'✅' if alerts_db_path.exists() else '❌'}")
    
    if alerts_db_path.exists():
        print(f"   数据库大小: {alerts_db_path.stat().st_size} 字节")

if __name__ == "__main__":
    test_signal_data()
