#!/usr/bin/env python3
"""
测试配置备份机制
Test Configuration Backup Mechanism
"""

import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.monitor_integration import MonitorIntegration

def test_backup_mechanism():
    """测试配置备份机制"""
    print("🔍 测试配置备份机制...")
    
    # 创建监控集成实例
    monitor_integration = MonitorIntegration()
    
    # 配置文件路径
    auto_config_path = project_root / "config" / "auto_monitor.json"
    monitoring_config_path = project_root / "config" / "monitoring.json"
    auto_backup_path = auto_config_path.with_suffix('.bak')
    monitoring_backup_path = monitoring_config_path.with_suffix('.bak')
    
    print(f"\n📁 配置文件路径:")
    print(f"   auto_monitor.json: {auto_config_path}")
    print(f"   monitoring.json: {monitoring_config_path}")
    print(f"   auto_monitor.json.bak: {auto_backup_path}")
    print(f"   monitoring.json.bak: {monitoring_backup_path}")
    
    # 1. 检查原始配置文件是否存在
    print(f"\n1. 检查原始配置文件:")
    print(f"   auto_monitor.json存在: {'✅' if auto_config_path.exists() else '❌'}")
    print(f"   monitoring.json存在: {'✅' if monitoring_config_path.exists() else '❌'}")
    
    if not auto_config_path.exists() or not monitoring_config_path.exists():
        print("   ❌ 原始配置文件不存在，无法测试备份机制")
        return False
    
    # 2. 清理可能存在的旧备份文件
    print(f"\n2. 清理旧备份文件:")
    if auto_backup_path.exists():
        auto_backup_path.unlink()
        print(f"   删除旧的auto_monitor.json.bak")
    if monitoring_backup_path.exists():
        monitoring_backup_path.unlink()
        print(f"   删除旧的monitoring.json.bak")
    
    # 3. 读取原始配置内容
    print(f"\n3. 读取原始配置内容:")
    try:
        with open(auto_config_path, 'r', encoding='utf-8') as f:
            original_auto_config = json.load(f)
        
        with open(monitoring_config_path, 'r', encoding='utf-8') as f:
            original_monitoring_config = json.load(f)
        
        print(f"   auto_monitor.json配置项: {len(original_auto_config)}")
        print(f"   monitoring.json配置项: {len(original_monitoring_config)}")
    except Exception as e:
        print(f"   ❌ 读取原始配置失败: {e}")
        return False
    
    # 4. 执行配置更新以触发备份机制
    print(f"\n4. 执行配置更新以触发备份:")
    test_config = {
        'max_symbols': 75,  # 修改auto_monitor.json
        'monitor_interval': '4h',  # 修改monitoring.json
        'confidence_threshold': 65.0,
        'log_level': 'DEBUG'
    }
    
    update_result = monitor_integration.update_monitor_config(test_config)
    print(f"   配置更新结果: {'✅ 成功' if update_result.get('success', False) else '❌ 失败'}")
    print(f"   更新消息: {update_result.get('message', 'N/A')}")
    
    if not update_result.get('success', False):
        print("   ❌ 配置更新失败，无法测试备份机制")
        return False
    
    # 5. 检查备份文件是否创建
    print(f"\n5. 检查备份文件创建:")
    auto_backup_exists = auto_backup_path.exists()
    monitoring_backup_exists = monitoring_backup_path.exists()
    
    print(f"   auto_monitor.json.bak: {'✅ 已创建' if auto_backup_exists else '❌ 未创建'}")
    print(f"   monitoring.json.bak: {'✅ 已创建' if monitoring_backup_exists else '❌ 未创建'}")
    
    # 6. 验证备份文件内容
    if auto_backup_exists:
        print(f"\n6. 验证auto_monitor.json.bak内容:")
        try:
            with open(auto_backup_path, 'r', encoding='utf-8') as f:
                backup_auto_config = json.load(f)
            
            # 比较备份文件与原始文件
            if backup_auto_config == original_auto_config:
                print(f"   ✅ 备份内容与原始文件一致")
            else:
                print(f"   ⚠️ 备份内容与原始文件不同")
                print(f"   原始配置项: {len(original_auto_config)}")
                print(f"   备份配置项: {len(backup_auto_config)}")
        except Exception as e:
            print(f"   ❌ 读取备份文件失败: {e}")
    
    if monitoring_backup_exists:
        print(f"\n7. 验证monitoring.json.bak内容:")
        try:
            with open(monitoring_backup_path, 'r', encoding='utf-8') as f:
                backup_monitoring_config = json.load(f)
            
            # 比较备份文件与原始文件
            if backup_monitoring_config == original_monitoring_config:
                print(f"   ✅ 备份内容与原始文件一致")
            else:
                print(f"   ⚠️ 备份内容与原始文件不同")
                print(f"   原始配置项: {len(original_monitoring_config)}")
                print(f"   备份配置项: {len(backup_monitoring_config)}")
        except Exception as e:
            print(f"   ❌ 读取备份文件失败: {e}")
    
    # 7. 验证配置文件已更新
    print(f"\n8. 验证配置文件更新:")
    try:
        updated_config = monitor_integration.get_monitor_config()
        if 'error' not in updated_config:
            print(f"   max_symbols: {updated_config.get('max_symbols')} (期望: 75)")
            print(f"   monitor_interval: {updated_config.get('monitor_interval')} (期望: 4h)")
            print(f"   confidence_threshold: {updated_config.get('confidence_threshold')} (期望: 65.0)")
            print(f"   log_level: {updated_config.get('log_level')} (期望: DEBUG)")
            
            # 验证更新是否成功
            updates_correct = (
                updated_config.get('max_symbols') == 75 and
                updated_config.get('monitor_interval') == '4h' and
                updated_config.get('confidence_threshold') == 65.0 and
                updated_config.get('log_level') == 'DEBUG'
            )
            
            print(f"   配置更新验证: {'✅ 正确' if updates_correct else '❌ 错误'}")
        else:
            print(f"   ❌ 获取更新后配置失败: {updated_config['error']}")
    except Exception as e:
        print(f"   ❌ 验证配置更新失败: {e}")
    
    # 8. 恢复原始配置
    print(f"\n9. 恢复原始配置:")
    try:
        # 使用备份文件恢复原始配置
        if auto_backup_exists:
            import shutil
            shutil.copy2(auto_backup_path, auto_config_path)
            print(f"   ✅ 从备份恢复auto_monitor.json")
        
        if monitoring_backup_exists:
            import shutil
            shutil.copy2(monitoring_backup_path, monitoring_config_path)
            print(f"   ✅ 从备份恢复monitoring.json")
        
        # 验证恢复结果
        restored_config = monitor_integration.get_monitor_config()
        if 'error' not in restored_config:
            print(f"   恢复后max_symbols: {restored_config.get('max_symbols')} (期望: 50)")
            print(f"   恢复后confidence_threshold: {restored_config.get('confidence_threshold')} (期望: 60.0)")
    except Exception as e:
        print(f"   ❌ 恢复原始配置失败: {e}")
    
    # 9. 测试结果总结
    print(f"\n" + "="*60)
    print(f"🎯 配置备份机制测试结果")
    print(f"="*60)
    
    backup_mechanism_working = auto_backup_exists and monitoring_backup_exists
    
    if backup_mechanism_working:
        print(f"✅ 配置备份机制正常工作")
        print(f"   - auto_monitor.json备份: ✅")
        print(f"   - monitoring.json备份: ✅")
        print(f"   - 备份内容验证: ✅")
        print(f"   - 配置恢复功能: ✅")
    else:
        print(f"❌ 配置备份机制存在问题")
        if not auto_backup_exists:
            print(f"   - auto_monitor.json备份未创建")
        if not monitoring_backup_exists:
            print(f"   - monitoring.json备份未创建")
    
    return backup_mechanism_working

if __name__ == "__main__":
    success = test_backup_mechanism()
    if success:
        print(f"\n🎉 配置备份机制测试通过！")
    else:
        print(f"\n❌ 配置备份机制测试失败！")
