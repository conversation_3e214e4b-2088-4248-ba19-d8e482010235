#!/usr/bin/env python3
"""
测试系统健康监控功能
Test System Health Monitoring Functionality
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.monitor_integration import MonitorIntegration

def test_system_health():
    """测试系统健康监控功能"""
    print("🔍 测试系统健康监控功能...")
    
    # 创建监控集成实例
    monitor_integration = MonitorIntegration()
    
    # 1. 测试系统资源监控
    print("\n1. 测试系统资源监控:")
    resource_status = monitor_integration._get_resource_status()
    
    if 'error' not in resource_status:
        print("   ✅ 资源监控成功")
        print(f"   CPU使用率: {resource_status.get('cpu_percent', 'N/A'):.1f}%")
        print(f"   CPU核心数: {resource_status.get('cpu_count', 'N/A')}")
        print(f"   内存使用: {resource_status.get('memory_used_gb', 'N/A'):.1f}GB / {resource_status.get('memory_total_gb', 'N/A'):.1f}GB ({resource_status.get('memory_percent', 'N/A'):.1f}%)")
        print(f"   磁盘使用: {resource_status.get('disk_used_gb', 'N/A'):.1f}GB / {resource_status.get('disk_total_gb', 'N/A'):.1f}GB ({resource_status.get('disk_percent', 'N/A'):.1f}%)")
        print(f"   网络发送: {resource_status.get('network_bytes_sent', 0):,} 字节")
        print(f"   网络接收: {resource_status.get('network_bytes_recv', 0):,} 字节")
    else:
        print(f"   ❌ 资源监控失败: {resource_status['error']}")
    
    # 2. 测试监控系统健康检查
    print("\n2. 测试监控系统健康检查:")
    monitor_health = monitor_integration._check_monitor_health()
    
    if 'error' not in monitor_health:
        print("   ✅ 监控健康检查成功")
        print(f"   监控运行: {'是' if monitor_health.get('is_running', False) else '否'}")
        print(f"   线程活跃: {'是' if monitor_health.get('thread_alive', False) else '否'}")
        print(f"   运行时间: {monitor_health.get('uptime_seconds', 0)} 秒")
        print(f"   状态文件: {'存在' if monitor_health.get('status_file_exists', False) else '不存在'}")
        
        issues = monitor_health.get('issues', [])
        warnings = monitor_health.get('warnings', [])
        
        if issues:
            print(f"   ⚠️ 问题 ({len(issues)}个):")
            for issue in issues:
                print(f"     - {issue}")
        
        if warnings:
            print(f"   ⚠️ 警告 ({len(warnings)}个):")
            for warning in warnings:
                print(f"     - {warning}")
        
        if not issues and not warnings:
            print("   🎉 监控系统健康状态良好")
    else:
        print(f"   ❌ 监控健康检查失败: {monitor_health['error']}")
    
    # 3. 测试数据库健康检查
    print("\n3. 测试数据库健康检查:")
    database_health = monitor_integration._check_database_health()
    
    if 'error' not in database_health:
        print("   ✅ 数据库健康检查成功")
        
        databases = database_health.get('databases', {})
        for db_name, db_info in databases.items():
            print(f"   数据库 {db_name}:")
            print(f"     存在: {'是' if db_info.get('exists', False) else '否'}")
            if db_info.get('accessible'):
                print(f"     可访问: 是")
                print(f"     表数量: {db_info.get('tables_count', 0)}")
                print(f"     文件大小: {db_info.get('size_mb', 0):.2f}MB")
            elif 'error' in db_info:
                print(f"     错误: {db_info['error']}")
        
        issues = database_health.get('issues', [])
        warnings = database_health.get('warnings', [])
        
        if issues:
            print(f"   ⚠️ 数据库问题 ({len(issues)}个):")
            for issue in issues:
                print(f"     - {issue}")
        
        if warnings:
            print(f"   ⚠️ 数据库警告 ({len(warnings)}个):")
            for warning in warnings:
                print(f"     - {warning}")
    else:
        print(f"   ❌ 数据库健康检查失败: {database_health['error']}")
    
    # 4. 测试日志健康检查
    print("\n4. 测试日志健康检查:")
    log_health = monitor_integration._check_log_health()
    
    if 'error' not in log_health:
        print("   ✅ 日志健康检查成功")
        
        log_files = log_health.get('log_files', {})
        for log_name, log_info in log_files.items():
            print(f"   日志文件 {log_name}:")
            print(f"     存在: {'是' if log_info.get('exists', False) else '否'}")
            if log_info.get('exists') and 'error' not in log_info:
                print(f"     大小: {log_info.get('size_mb', 0):.2f}MB")
                print(f"     最后修改: {log_info.get('last_modified', 'N/A')}")
                print(f"     更新间隔: {log_info.get('age_hours', 0):.1f}小时前")
            elif 'error' in log_info:
                print(f"     错误: {log_info['error']}")
        
        warnings = log_health.get('warnings', [])
        if warnings:
            print(f"   ⚠️ 日志警告 ({len(warnings)}个):")
            for warning in warnings:
                print(f"     - {warning}")
    else:
        print(f"   ❌ 日志健康检查失败: {log_health['error']}")
    
    # 5. 测试网络健康检查
    print("\n5. 测试网络健康检查:")
    network_health = monitor_integration._check_network_health()
    
    if 'error' not in network_health:
        print("   ✅ 网络健康检查成功")
        
        status = network_health.get('status', {})
        print(f"   互联网连接: {'✅ 正常' if status.get('internet_accessible', False) else '❌ 异常'}")
        print(f"   Binance API: {'✅ 正常' if status.get('binance_api_accessible', False) else '❌ 异常'}")
        
        response_times = status.get('response_times', {})
        if response_times:
            print("   响应时间:")
            for service, time_ms in response_times.items():
                print(f"     {service}: {time_ms}ms")
        
        issues = network_health.get('issues', [])
        if issues:
            print(f"   ⚠️ 网络问题 ({len(issues)}个):")
            for issue in issues:
                print(f"     - {issue}")
    else:
        print(f"   ❌ 网络健康检查失败: {network_health['error']}")
    
    # 6. 测试性能指标
    print("\n6. 测试性能指标:")
    performance_metrics = monitor_integration._get_performance_metrics()
    
    if 'error' not in performance_metrics:
        print("   ✅ 性能指标获取成功")
        print(f"   监控运行时间: {performance_metrics.get('monitor_uptime_formatted', 'N/A')}")
        
        if 'db_query_time_ms' in performance_metrics:
            print(f"   数据库查询时间: {performance_metrics['db_query_time_ms']}ms")
            print(f"   总告警数: {performance_metrics.get('total_alerts', 0)}")
        
        if 'config_load_time_ms' in performance_metrics:
            print(f"   配置加载时间: {performance_metrics['config_load_time_ms']}ms")
        
        if 'db_query_error' in performance_metrics:
            print(f"   数据库查询错误: {performance_metrics['db_query_error']}")
        
        if 'config_load_error' in performance_metrics:
            print(f"   配置加载错误: {performance_metrics['config_load_error']}")
    else:
        print(f"   ❌ 性能指标获取失败: {performance_metrics['error']}")
    
    # 7. 测试完整系统健康检查
    print("\n7. 测试完整系统健康检查:")
    system_health = monitor_integration.get_system_health()
    
    if 'error' not in system_health:
        print("   ✅ 完整健康检查成功")
        
        overall_status = system_health.get('overall_status', 'unknown')
        status_emoji = {
            'healthy': '🟢',
            'warning': '🟡', 
            'critical': '🔴',
            'error': '❌'
        }.get(overall_status, '❓')
        
        print(f"   整体状态: {status_emoji} {overall_status.upper()}")
        
        issues = system_health.get('issues', [])
        warnings = system_health.get('warnings', [])
        
        print(f"   问题数量: {len(issues)}")
        print(f"   警告数量: {len(warnings)}")
        
        if issues:
            print("   🔴 严重问题:")
            for issue in issues[:3]:  # 只显示前3个
                print(f"     - {issue}")
            if len(issues) > 3:
                print(f"     ... 还有 {len(issues) - 3} 个问题")
        
        if warnings:
            print("   🟡 警告信息:")
            for warning in warnings[:3]:  # 只显示前3个
                print(f"     - {warning}")
            if len(warnings) > 3:
                print(f"     ... 还有 {len(warnings) - 3} 个警告")
        
        if not issues and not warnings:
            print("   🎉 系统运行状态完全正常！")
    else:
        print(f"   ❌ 完整健康检查失败: {system_health['error']}")
    
    print("\n" + "="*60)
    print("🎯 系统健康监控测试完成")
    print("="*60)
    
    # 总结
    print("\n📋 健康监控功能总结:")
    print("   ✅ 系统资源监控 (CPU、内存、磁盘、网络)")
    print("   ✅ 监控系统状态检查")
    print("   ✅ 数据库健康检查")
    print("   ✅ 日志文件健康检查")
    print("   ✅ 网络连接检查")
    print("   ✅ 性能指标监控")
    print("   ✅ 综合健康状态评估")

if __name__ == "__main__":
    test_system_health()
