#!/usr/bin/env python3
"""
测试配置热更新功能
Test Configuration Hot Update Functionality
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.monitor_integration import MonitorIntegration

def test_config_hot_update():
    """测试配置热更新功能"""
    print("🔍 测试配置热更新功能...")
    
    # 创建监控集成实例
    monitor_integration = MonitorIntegration()
    
    # 1. 测试在监控系统未运行时的热更新
    print("\n1. 测试监控系统未运行时的热更新:")
    hot_update_config = {
        'log_level': 'DEBUG',
        'enable_signal_notification': False
    }
    
    hot_update_result = monitor_integration.apply_config_hot_update(hot_update_config)
    print(f"   热更新结果: {'❌ 正确拒绝' if not hot_update_result.get('success', True) else '✅ 错误允许'}")
    print(f"   消息: {hot_update_result.get('message', 'N/A')}")
    
    # 2. 启动监控系统
    print("\n2. 启动监控系统:")
    start_result = monitor_integration.start_monitor()
    print(f"   启动结果: {'✅ 成功' if start_result.get('success', False) else '❌ 失败'}")
    
    if not start_result.get('success', False):
        print("   无法启动监控系统，跳过后续测试")
        return
    
    # 等待监控系统完全启动
    print("   等待监控系统启动...")
    time.sleep(3)
    
    # 3. 测试支持热更新的配置
    print("\n3. 测试支持热更新的配置:")
    hot_updateable_config = {
        'log_level': 'DEBUG',
        'enable_signal_notification': False,
        'enable_error_notification': True,
        'max_signals_per_hour': 20
    }
    
    hot_update_result = monitor_integration.apply_config_hot_update(hot_updateable_config)
    print(f"   热更新结果: {'✅ 成功' if hot_update_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {hot_update_result.get('message', 'N/A')}")
    print(f"   应用配置数: {hot_update_result.get('applied_count', 0)}")
    print(f"   跳过配置数: {hot_update_result.get('skipped_count', 0)}")
    
    # 4. 测试不支持热更新的配置
    print("\n4. 测试不支持热更新的配置:")
    non_hot_updateable_config = {
        'max_symbols': 100,
        'monitor_interval': '2h',
        'min_volume_usdt': 2000000
    }
    
    non_hot_update_result = monitor_integration.apply_config_hot_update(non_hot_updateable_config)
    print(f"   热更新结果: {'✅ 成功' if non_hot_update_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {non_hot_update_result.get('message', 'N/A')}")
    print(f"   应用配置数: {non_hot_update_result.get('applied_count', 0)}")
    print(f"   跳过配置数: {non_hot_update_result.get('skipped_count', 0)}")
    
    # 5. 测试混合配置（部分支持热更新，部分不支持）
    print("\n5. 测试混合配置:")
    mixed_config = {
        'log_level': 'WARNING',  # 支持热更新
        'enable_signal_notification': True,  # 支持热更新
        'max_symbols': 80,  # 不支持热更新
        'monitor_interval': '6h'  # 不支持热更新
    }
    
    mixed_update_result = monitor_integration.apply_config_hot_update(mixed_config)
    print(f"   混合更新结果: {'✅ 成功' if mixed_update_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {mixed_update_result.get('message', 'N/A')}")
    print(f"   应用配置数: {mixed_update_result.get('applied_count', 0)}")
    print(f"   跳过配置数: {mixed_update_result.get('skipped_count', 0)}")
    
    # 6. 测试配置文件更新 + 重启
    print("\n6. 测试配置文件更新 + 重启:")
    
    # 首先更新配置文件
    restart_config = {
        'max_symbols': 75,
        'monitor_interval': '2h',
        'confidence_threshold': 75.0
    }
    
    config_update_result = monitor_integration.update_monitor_config(restart_config)
    print(f"   配置文件更新: {'✅ 成功' if config_update_result.get('success', False) else '❌ 失败'}")
    print(f"   需要重启: {'是' if config_update_result.get('requires_restart', False) else '否'}")
    
    # 然后重启监控系统
    if config_update_result.get('success', False):
        print("   正在重启监控系统...")
        restart_result = monitor_integration.restart_monitor_with_new_config()
        print(f"   重启结果: {'✅ 成功' if restart_result.get('success', False) else '❌ 失败'}")
        print(f"   消息: {restart_result.get('message', 'N/A')}")
        
        if restart_result.get('success', False):
            # 等待重启完成
            time.sleep(3)
            
            # 验证新配置是否生效
            print("   验证新配置:")
            new_config = monitor_integration.get_monitor_config()
            if 'error' not in new_config:
                print(f"     最大币种数: {new_config.get('max_symbols', 'N/A')} (期望: 75)")
                print(f"     监控间隔: {new_config.get('monitor_interval', 'N/A')} (期望: 2h)")
                print(f"     置信度阈值: {new_config.get('confidence_threshold', 'N/A')}% (期望: 75.0%)")
            else:
                print(f"     ❌ 获取新配置失败: {new_config['error']}")
    
    # 7. 测试在监控系统未运行时的重启
    print("\n7. 测试监控系统未运行时的重启:")
    
    # 先停止监控系统
    stop_result = monitor_integration.stop_monitor()
    print(f"   停止监控系统: {'✅ 成功' if stop_result.get('success', False) else '❌ 失败'}")
    
    time.sleep(1)
    
    # 尝试重启未运行的系统
    invalid_restart_result = monitor_integration.restart_monitor_with_new_config()
    print(f"   重启未运行系统: {'❌ 正确拒绝' if not invalid_restart_result.get('success', True) else '✅ 错误允许'}")
    print(f"   消息: {invalid_restart_result.get('message', 'N/A')}")
    
    # 8. 恢复原始配置
    print("\n8. 恢复原始配置:")
    restore_config = {
        'max_symbols': 50,
        'monitor_interval': '4h',
        'confidence_threshold': 60.0,
        'log_level': 'INFO',
        'enable_signal_notification': True,
        'enable_error_notification': True
    }
    
    restore_result = monitor_integration.update_monitor_config(restore_config)
    print(f"   恢复配置: {'✅ 成功' if restore_result.get('success', False) else '❌ 失败'}")
    
    print("\n" + "="*60)
    print("🎯 配置热更新测试完成")
    print("="*60)
    
    # 总结
    print("\n📋 功能总结:")
    print("   ✅ 配置文件读取和保存")
    print("   ✅ 配置参数验证")
    print("   ✅ 配置热更新（部分参数）")
    print("   ✅ 监控系统重启应用新配置")
    print("   ✅ 错误处理和状态检查")

if __name__ == "__main__":
    test_config_hot_update()
