#!/usr/bin/env python3
"""
测试缓存系统功能
Test Cache System Functionality
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def test_cache_system():
    """测试缓存系统"""
    print("🔍 测试缓存系统功能...")
    
    # 创建集成实例
    integration = TBTradeIntegration()
    
    # 1. 测试初始缓存状态
    print("\n1. 初始缓存状态:")
    cache_status = integration.get_cache_status()
    print(f"   缓存项数量: {cache_status['total_cached_items']}")
    print(f"   缓存详情: {cache_status['cache_details']}")
    
    # 2. 测试数据获取和缓存
    print("\n2. 测试数据获取和缓存:")
    
    # 第一次调用 - 应该从数据库获取并缓存
    print("   第一次获取币种列表...")
    start_time = time.time()
    symbols1 = integration.get_available_symbols()
    time1 = time.time() - start_time
    print(f"   获取到 {len(symbols1)} 个币种，耗时: {time1:.3f}秒")
    
    # 第二次调用 - 应该从缓存获取
    print("   第二次获取币种列表...")
    start_time = time.time()
    symbols2 = integration.get_available_symbols()
    time2 = time.time() - start_time
    print(f"   获取到 {len(symbols2)} 个币种，耗时: {time2:.3f}秒")
    
    print(f"   缓存效果: 第二次调用快了 {(time1-time2)/time1*100:.1f}%")
    print(f"   数据一致性: {'✅ 一致' if symbols1 == symbols2 else '❌ 不一致'}")
    
    # 3. 测试缓存状态
    print("\n3. 缓存状态检查:")
    cache_status = integration.get_cache_status()
    print(f"   缓存项数量: {cache_status['total_cached_items']}")
    
    for key, details in cache_status['cache_details'].items():
        print(f"   {key}:")
        print(f"     - 有效: {'✅' if details['valid'] else '❌'}")
        print(f"     - 年龄: {details['age_seconds']:.1f}秒")
        print(f"     - TTL: {details['ttl_seconds']}秒")
        print(f"     - 剩余: {details['expires_in']:.1f}秒")
    
    # 4. 测试数据库状态缓存
    print("\n4. 测试数据库状态缓存:")
    
    print("   第一次检查数据库状态...")
    start_time = time.time()
    db_status1 = integration.check_database_connection()
    time1 = time.time() - start_time
    print(f"   连接状态: {'✅' if db_status1['connected'] else '❌'}，耗时: {time1:.3f}秒")
    
    print("   第二次检查数据库状态...")
    start_time = time.time()
    db_status2 = integration.check_database_connection()
    time2 = time.time() - start_time
    print(f"   连接状态: {'✅' if db_status2['connected'] else '❌'}，耗时: {time2:.3f}秒")
    
    print(f"   缓存效果: 第二次调用快了 {(time1-time2)/time1*100:.1f}%")
    
    # 5. 测试缓存清除
    print("\n5. 测试缓存清除:")
    print("   清除币种列表缓存...")
    integration._clear_cache('symbols')
    
    cache_status = integration.get_cache_status()
    print(f"   清除后缓存项数量: {cache_status['total_cached_items']}")
    
    # 再次获取应该重新从数据库读取
    print("   清除后重新获取币种列表...")
    start_time = time.time()
    symbols3 = integration.get_available_symbols()
    time3 = time.time() - start_time
    print(f"   获取到 {len(symbols3)} 个币种，耗时: {time3:.3f}秒")
    print(f"   与缓存前对比: 耗时相近说明重新从数据库获取")
    
    # 6. 测试数据信息缓存
    print("\n6. 测试数据信息缓存:")
    
    print("   获取数据概览...")
    start_time = time.time()
    data_info1 = integration.get_data_info()
    time1 = time.time() - start_time
    print(f"   获取完成，耗时: {time1:.3f}秒")
    
    print("   再次获取数据概览...")
    start_time = time.time()
    data_info2 = integration.get_data_info()
    time2 = time.time() - start_time
    print(f"   获取完成，耗时: {time2:.3f}秒")
    
    print(f"   缓存效果: 第二次调用快了 {(time1-time2)/time1*100:.1f}%")
    
    # 7. 最终缓存状态
    print("\n7. 最终缓存状态:")
    cache_status = integration.get_cache_status()
    print(f"   总缓存项: {cache_status['total_cached_items']}")
    print(f"   缓存键: {list(cache_status['cache_details'].keys())}")

if __name__ == "__main__":
    test_cache_system()
